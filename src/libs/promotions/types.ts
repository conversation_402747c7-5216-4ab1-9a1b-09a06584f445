import type { OfferType } from '@/types';
import type { PromoType } from '@/types/common';
import type { CartItemType } from '@/libs/cart/types';

export type PromotionItemType = CartItemType & {
  freeItemsQty: number;
  freeOffer: OfferType | null;
};

export type BuyXGetYPromotionData = {
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
  freeOffer: OfferType | null;
  promotion: PromoType | null;
  items: PromotionItemType[];
  imageUrl: string;
  manufacturer: string;
};

export type ProcessedPromotionData = {
  promotions: Record<string, { items: CartItemType[] }> & {
    buy_x_get_y?: BuyXGetYPromotionData;
  };
  nonPromotionItems: CartItemType[];
};
